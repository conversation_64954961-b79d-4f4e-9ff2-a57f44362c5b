#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Outlook注册工具启动器
"""

import os
import sys
import json
import subprocess
import importlib.util

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("❌ 错误：需要Python 3.7或更高版本")
        print(f"当前版本：{sys.version}")
        return False
    print(f"✅ Python版本检查通过：{sys.version.split()[0]}")
    return True

def check_required_files():
    """检查必要文件"""
    required_files = ['OutlookRegister.py', 'config.json']
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ 错误：未找到必要文件 {file}")
            return False
    print("✅ 必要文件检查通过")
    return True

def check_dependencies():
    """检查Python依赖包"""
    required_packages = {
        'playwright': 'playwright',
        'faker': 'faker',
        'requests': 'requests'
    }
    
    missing_packages = []
    for package_name, import_name in required_packages.items():
        if importlib.util.find_spec(import_name) is None:
            missing_packages.append(package_name)
    
    if missing_packages:
        print(f"❌ 缺少依赖包：{', '.join(missing_packages)}")
        print("正在安装依赖包...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + missing_packages)
            print("✅ 依赖包安装成功")
            
            # 安装Playwright浏览器
            print("正在安装Playwright浏览器...")
            subprocess.check_call([sys.executable, '-m', 'playwright', 'install', 'chromium'])
            print("✅ Playwright浏览器安装成功")
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 依赖包安装失败：{e}")
            return False
    else:
        print("✅ 依赖包检查通过")
    
    return True

def show_config():
    """显示当前配置"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("\n当前配置信息：")
        print(f"  代理设置: {config.get('proxy', '未配置')}")
        print(f"  并发数: {config.get('concurrent_flows', 1)}")
        print(f"  最大任务数: {config.get('max_tasks', 5)}")
        print(f"  机器人保护等待时间: {config.get('Bot_protection_wait', 15)}秒")
        print(f"  浏览器路径: {config.get('browser_path', '默认')}")
        print(f"  启用OAuth2: {config.get('enable_oauth2', False)}")
        
        # 代理建议
        proxy = config.get('proxy', '').strip()
        if not proxy:
            print("\n⚠️  注意：未配置代理")
            print("   - 不使用代理可能导致IP被快速限制")
            print("   - 建议配置代理以提高成功率")
            print("   - 如需配置代理，请修改config.json中的proxy字段")
        
        return True
    except Exception as e:
        print(f"❌ 读取配置文件失败：{e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("           Outlook注册工具启动器")
    print("=" * 50)
    print()
    
    # 检查Python版本
    if not check_python_version():
        input("按回车键退出...")
        return
    
    # 检查必要文件
    if not check_required_files():
        input("按回车键退出...")
        return
    
    # 检查依赖包
    print("\n正在检查Python依赖包...")
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    # 显示配置
    if not show_config():
        input("按回车键退出...")
        return
    
    # 询问是否继续
    print()
    choice = input("是否开始运行注册工具？(Y/N): ").strip().lower()
    if choice not in ['y', 'yes', '是']:
        print("已取消运行")
        input("按回车键退出...")
        return
    
    print()
    print("=" * 50)
    print("           开始运行注册工具")
    print("=" * 50)
    print()
    
    # 运行主程序
    try:
        # 导入并运行主程序
        import OutlookRegister
        print("程序启动成功！")
    except KeyboardInterrupt:
        print("\n用户中断程序")
    except Exception as e:
        print(f"\n程序运行异常：{e}")
    
    print()
    print("=" * 50)
    print("           程序运行结束")
    print("=" * 50)
    input("按回车键退出...")

if __name__ == "__main__":
    main()
